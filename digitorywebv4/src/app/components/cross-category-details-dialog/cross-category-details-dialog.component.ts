import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';

export interface CrossCategoryDialogData {
  details: string;
}

@Component({
  selector: 'app-cross-category-details-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule
  ],
  template: `
    <div class="cross-category-dialog">
      <div class="dialog-header">
        <h2 mat-dialog-title>
          <mat-icon>swap_horiz</mat-icon>
          Cross-Category Transfer Details
        </h2>
        <button mat-icon-button mat-dialog-close class="close-button">
          <mat-icon>close</mat-icon>
        </button>
      </div>
      
      <mat-dialog-content class="dialog-content">
        <div class="details-container">
          <pre class="transfer-details">{{ data.details }}</pre>
        </div>
      </mat-dialog-content>
      
      <mat-dialog-actions class="dialog-actions">
        <button mat-raised-button mat-dialog-close color="primary">
          <mat-icon>check</mat-icon>
          Close
        </button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [`
    .cross-category-dialog {
      max-width: 100%;
      
      .dialog-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px 8px 24px;
        border-bottom: 1px solid #e0e0e0;
        
        h2 {
          margin: 0;
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 18px;
          font-weight: 500;
          color: #333;
          
          mat-icon {
            color: #ff6b35;
          }
        }
        
        .close-button {
          color: #666;
          
          &:hover {
            background-color: #f5f5f5;
          }
        }
      }
      
      .dialog-content {
        padding: 16px 24px;
        max-height: 60vh;
        overflow-y: auto;
        
        .details-container {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 16px;
          
          .transfer-details {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 13px;
            line-height: 1.6;
            color: #333;
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
            background: transparent;
            border: none;
          }
        }
      }
      
      .dialog-actions {
        padding: 8px 24px 16px 24px;
        justify-content: flex-end;
        border-top: 1px solid #e0e0e0;
        
        button {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 500;
        }
      }
    }
    
    /* Scrollbar styling */
    .dialog-content::-webkit-scrollbar {
      width: 6px;
    }
    
    .dialog-content::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    .dialog-content::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }
    
    .dialog-content::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  `]
})
export class CrossCategoryDetailsDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<CrossCategoryDetailsDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: CrossCategoryDialogData
  ) {}
}
