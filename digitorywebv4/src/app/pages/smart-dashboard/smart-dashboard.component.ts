import { <PERSON>mpo<PERSON>, On<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef, NgZone, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';

// Angular Material Modules
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatTableModule } from '@angular/material/table';
import { MatStepperModule } from '@angular/material/stepper';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';

// Third-party modules
import { NgChartsModule } from 'ng2-charts';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { ChartType, ChartData, ChartConfiguration } from 'chart.js';

// Services
import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';
import { ShareDataService } from '../../services/share-data.service';
import { DashboardConfigService, DashboardType, BaseDateOption } from '../../services/dashboard-config.service';
import { ChartRendererService, ChartModel } from '../../services/chart-renderer.service';
import { DepartmentService, Department, DepartmentCategoryMapping, DepartmentGroupCategoryMapping } from '../../services/department.service';
import { DropdownDefaultsService } from '../../services/dropdown-defaults.service';

// Components
import { UnifiedDepartmentMappingComponent, CategoryWorkareaMapping, WorkAreaData } from '../../components/unified-department-mapping/unified-department-mapping.component';

// Interfaces
interface SummaryCard {
  icon: string;
  value: string;
  label: string;
  color: string;
  data_type?: string;
  secondary_info?: string;
  formula?: string;
  trend?: string;
}

interface DashboardMode {
  value: string;
  label: string;
  icon: string;
  disabled?: boolean;
}

@Component({
  selector: 'app-smart-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatButtonToggleModule,
    MatTooltipModule,
    MatPaginatorModule,
    MatTableModule,
    MatStepperModule,
    MatDialogModule,
    MatSlideToggleModule,
    NgChartsModule,
    NgxMatSelectSearchModule,
    ReactiveFormsModule,
    FormsModule,
    UnifiedDepartmentMappingComponent
  ],
  templateUrl: './smart-dashboard.component.html',
  styleUrls: ['./smart-dashboard.component.scss']
})
export class SmartDashboardComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  // User and branch data
  user: any;
  branches: any[] = [];
  filteredBranches: any[] = [];

  // Categories and subcategories data
  categories: any[] = [];
  subcategories: any[] = [];
  filteredCategories: any[] = [];
  filteredSubcategories: any[] = [];

  // Work areas data
  workAreas: any[] = [];
  filteredWorkAreas: any[] = [];

  // Department data
  departments: Department[] = [];
  filteredDepartments: Department[] = [];
  departmentCategoryMappings: DepartmentCategoryMapping[] = [];
  departmentGroupCategoryMappings: DepartmentGroupCategoryMapping[] = [];

  // Category-WorkArea mapping data
  categoryWorkareaMappings: CategoryWorkareaMapping[] = [];
  selectedCategoriesForMapping: string[] = [];
  readonly selectedCategoriesForMappingCtrl = new FormControl<string[]>([]);

  // Dialog state
  showMappingDialog = false;

  // ViewChild reference to unified department mapping component
  @ViewChild(UnifiedDepartmentMappingComponent) unifiedMappingComponent?: UnifiedDepartmentMappingComponent;

  // Form controls
  readonly selectedLocationsCtrl = new FormControl<string[] | string | null>(null);
  readonly locationFilterCtrl = new FormControl('');
  readonly selectedCategoriesCtrl = new FormControl<string[]>([]);
  readonly categoryFilterCtrl = new FormControl('');
  readonly selectedSubcategoriesCtrl = new FormControl<string[]>([]);
  readonly subcategoryFilterCtrl = new FormControl('');
  readonly selectedWorkAreasCtrl = new FormControl<string[]>([]);
  readonly workAreaFilterCtrl = new FormControl('');
  readonly selectedDepartmentsCtrl = new FormControl<string[]>([]);
  readonly departmentFilterCtrl = new FormControl('');
  readonly startDate = new FormControl();
  readonly endDate = new FormControl();
  readonly searchQuery = new FormControl({ value: '', disabled: true });
  readonly baseDateCtrl = new FormControl();
  readonly dashboardModeCtrl = new FormControl('default');
  selectedDashboard = '';

  // Dashboard data
  summaryCards: SummaryCard[] = [];
  charts: ChartModel[] = [];
  isLoading = false;
  isConfigLoaded = false;

  // Floating navigation state
  isNavMinimized = false;

  // Filter loading states
  private isBranchesLoaded = false;
  private isCategoriesLoaded = false;
  private isSubcategoriesLoaded = false;
  private isWorkAreasLoaded = false;
  private dashboardLoadTimeout: any = null;
  private isDashboardLoadPending = false;
  private isDashboardApiCallInProgress = false;
  private isInitialCategorySetup = false;

  // Dynamic configuration data
  dashboardTypes: DashboardType[] = [];
  baseDateOptions: BaseDateOption[] = [];

  // Dashboard modes
  readonly dashboardModes: DashboardMode[] = [
    { value: 'default', label: 'Default', icon: 'dashboard' },
    { value: 'ask_digi_ai', label: 'Ask Digi AI', icon: 'smart_toy', disabled: true }
  ];

  // Cost Analysis Toggle
  costAnalysisMode: 'consumption' | 'indent' = 'consumption';
  isCostAnalysisToggleLoading = false;

  constructor(
    private readonly smartDashboardService: SmartDashboardService,
    private readonly authService: AuthService,
    private readonly shareDataService: ShareDataService,
    private readonly configService: DashboardConfigService,
    private readonly chartRenderer: ChartRendererService,
    private readonly departmentService: DepartmentService,
    private readonly dropdownDefaults: DropdownDefaultsService,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly ngZone: NgZone
  ) {
    this.user = this.authService.getCurrentUser();
  }

  private initializeConfig(): void {
    this.configService.loadConfig().subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.configService.setConfig(response.data);
          this.setupDynamicConfigurations(response.data);
        } else {
          this.setupDefaultConfigurations();
        }
        this.isConfigLoaded = true;
        // For reconciliation dashboard, don't auto-load data on initialization
        const shouldAutoLoad = this.selectedDashboard !== 'reconciliation';
        this.loadCategoriesAndSubcategories(shouldAutoLoad);
        if (this.selectedDashboard === 'reconciliation') {
          this.loadDepartments();
        }

        this.cdr.detectChanges();
      },
      error: () => {
        this.setupDefaultConfigurations();
        this.isConfigLoaded = true;
        // For reconciliation dashboard, don't auto-load data on initialization
        const shouldAutoLoad = this.selectedDashboard !== 'reconciliation';
        this.loadCategoriesAndSubcategories(shouldAutoLoad);

        if (this.selectedDashboard === 'reconciliation') {
          this.loadDepartments();
        }

        this.cdr.detectChanges();
      }
    });
  }

  private setupDynamicConfigurations(config: any): void {
    const originalTypes = config.dashboard_types || [];
    this.dashboardTypes = originalTypes.sort((a: any, b: any) => {
      if (a.value === 'inventory') return -1;
      if (b.value === 'inventory') return 1;
      return 0;
    });

    this.baseDateOptions = config.base_date_options || [];

    const urlDashboard = this.route.snapshot.queryParams['dashboard'];
    const configDefault = config.ui_config?.default_dashboard_type;

    if (urlDashboard) {
      this.selectedDashboard = urlDashboard;
    } else if (configDefault && this.dashboardTypes.length > 0) {
      this.selectedDashboard = configDefault;
      this.router.navigate([], {
        relativeTo: this.route,
        queryParams: { dashboard: configDefault },
        queryParamsHandling: 'merge'
      });
    } else {
      this.selectedDashboard = '';
    }

    if (config.ui_config?.default_base_date && this.baseDateOptions.length > 0) {
      this.baseDateCtrl.setValue(config.ui_config.default_base_date);
    }

    if (this.selectedDashboard) {
      this.setDefaultDateRange();
      // Set default location selection now that dashboard is determined
      if (this.branches.length > 0) {
        this.setDefaultLocationSelection();
      }
    }
  }

  private setupDefaultConfigurations(): void {
    this.dashboardTypes = [];
    this.baseDateOptions = [];

    const urlDashboard = this.route.snapshot.queryParams['dashboard'];
    if (urlDashboard) {
      this.selectedDashboard = urlDashboard;
      // Set default location selection now that dashboard is determined
      if (this.branches.length > 0) {
        this.setDefaultLocationSelection();
      }
    } else {
      this.selectedDashboard = '';
    }


  }

  ngOnInit(): void {
    this.user = this.authService.getCurrentUser();

    // Set dashboard from URL immediately to ensure it's available for all subsequent operations
    const urlDashboard = this.route.snapshot.queryParams['dashboard'];
    if (urlDashboard) {
      this.selectedDashboard = urlDashboard;
    }

    this.initializeConfig();
    this.initializeFilters();
    this.loadBranchesOnly();
    this.loadCategoryWorkareaMappings();
  }

  ngOnDestroy(): void {
    if (this.dashboardLoadTimeout) {
      clearTimeout(this.dashboardLoadTimeout);
    }

    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeFilters(): void {
    this.locationFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterBranches(value || '');
      });

    this.categoryFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterCategories(value || '');
      });

    this.subcategoryFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterSubcategories(value || '');
      });

    this.workAreaFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterWorkAreas(value || '');
      });

    this.departmentFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterDepartments(value || '');
      });

    this.selectedCategoriesForMappingCtrl.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((selectedCategories: string[] | null) => {
        this.selectedCategoriesForMapping = selectedCategories || [];
      });

    this.selectedLocationsCtrl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((selectedLocations: string[] | string | null) => {
        // Validate location selection limits for inventory and purchase dashboards
        this.validateLocationSelection(selectedLocations);

        if (this.selectedDashboard === 'reconciliation') {
          this.loadWorkAreasFromBranches(false);
        } else {
          const hasSelection = selectedLocations && Array.isArray(selectedLocations) && selectedLocations.length > 0;

          if (hasSelection) {
            this.loadWorkAreasFromBranches(false);
          } else {
            this.workAreas = [];
            this.filteredWorkAreas = [];
            this.selectedWorkAreasCtrl.setValue([]);
            this.isWorkAreasLoaded = false;
          }
        }
      });

    this.selectedCategoriesCtrl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((selectedCategories: string[] | null) => {
        if (this.isInitialCategorySetup) {
          return;
        }

        if (selectedCategories && selectedCategories.length > 0) {
          this.loadSubcategoriesForSelectedCategories(false);
        }
      });
  }

  /**
   * Load branches without triggering dashboard loading (used during initialization)
   */
  private loadBranchesOnly(): void {
    this.shareDataService.selectedBranchesSource
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.branches = data || [];
        this.filteredBranches = [...this.branches];

        // Always set default location selection since dashboard should be set in ngOnInit
        this.setDefaultLocationSelection();
        this.loadWorkAreasFromBranches(false);
        this.isBranchesLoaded = true;
      });
  }

  /**
   * Load work areas from user's restaurant access data
   */
  private loadWorkAreasFromBranches(scheduleDashboard: boolean = true): void {
    const selectedLocations = this.getLocationsAsArray();
    const workAreaData: any[] = [];

    if (this.user && this.user.restaurantAccess) {
      this.user.restaurantAccess.forEach((restaurant: any) => {
        let shouldInclude = false;

        if (this.selectedDashboard === 'reconciliation') {
          shouldInclude = true;
        } else {
          shouldInclude = selectedLocations && selectedLocations.includes(restaurant.restaurantIdOld);
        }

        if (shouldInclude && restaurant.workAreas && restaurant.workAreas.length > 0) {
          workAreaData.push({
            restaurantIdOld: restaurant.restaurantIdOld,
            branchName: restaurant.branchName,
            workAreas: restaurant.workAreas,
            disabled: false
          });
        }
      });
    }

    this.workAreas = workAreaData;
    this.filteredWorkAreas = [...this.workAreas];

    this.setDefaultWorkAreaSelection();
    this.isWorkAreasLoaded = true;

    if (scheduleDashboard) {
      this.scheduleDashboardLoad();
    }
  }

  /**
   * Load departments for reconciliation dashboard
   */
  private loadDepartments(): void {
    if (this.selectedDashboard === 'reconciliation') {
      this.departmentService.getDepartments(this.user.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (departments) => {
            this.departments = departments;
            this.filteredDepartments = [...this.departments];
            this.setDefaultDepartmentSelection();
            this.loadDepartmentCategoryMappings();
            this.loadDepartmentGroupCategoryMappings();
            this.cdr.detectChanges();
          },
          error: () => {
            this.departments = [];
            this.filteredDepartments = [];
            this.cdr.detectChanges();
          }
        });
    }
  }

  /**
   * Load department-category mappings from API
   */
  private loadDepartmentCategoryMappings(): void {
    this.departmentService.getDepartmentCategoryMappings(this.user.tenantId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (mappings) => {
          this.departmentCategoryMappings = mappings;
          this.restoreDepartmentSelectionFromMappings();
        },
        error: (error) => {
          this.departmentCategoryMappings = [];
          this.selectedDepartmentsCtrl.setValue([]);
        }
      });
  }

  /**
   * Load department group-category mappings from API
   */
  private loadDepartmentGroupCategoryMappings(): void {
    this.departmentService.getDepartmentGroupCategoryMappings(this.user.tenantId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (mappings) => {
          this.departmentGroupCategoryMappings = mappings;
        },
        error: () => {
          this.departmentGroupCategoryMappings = [];
        }
      });
  }

  /**
   * Load category-workarea mappings from API
   */
  private loadCategoryWorkareaMappings(): void {
    if (!this.user?.tenantId) {
      return;
    }

    this.departmentService.getCategoryWorkareaMappings(this.user.tenantId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (mappings) => {
          this.categoryWorkareaMappings = mappings;
          this.updateWorkAreasFromMappings(mappings);
        },
        error: () => {
          this.categoryWorkareaMappings = [];
        }
      });
  }

  /**
   * Restore department selection based on existing mappings
   */
  private restoreDepartmentSelectionFromMappings(): void {
    if (this.departmentCategoryMappings && this.departmentCategoryMappings.length > 0) {
      const mappedDepartmentIds = this.departmentCategoryMappings.map(mapping => mapping.departmentId);

      const validDepartmentIds = mappedDepartmentIds.filter(id =>
        this.departments.some(dept => dept.id === id)
      );

      this.selectedDepartmentsCtrl.setValue(validDepartmentIds);
    } else {
      this.selectedDepartmentsCtrl.setValue([]);
    }
  }

  /**
   * Check if all filter configurations are loaded and load dashboard data if ready
   */
  private checkAndLoadDashboard(): void {
    if (this.isLoading || this.isDashboardLoadPending || this.isDashboardApiCallInProgress) {
      return;
    }

    if (this.isConfigLoaded && this.isBranchesLoaded && this.isCategoriesLoaded && this.isSubcategoriesLoaded && this.isWorkAreasLoaded) {
      if (this.dashboardTypes.length > 0 && this.selectedDashboard) {
        if (this.selectedDashboard === 'reconciliation') {
          // For reconciliation dashboard, don't auto-load data - user must click Search button
          this.isLoading = false;
          this.clearDashboardData();
          this.cdr.detectChanges();
        } else {
          this.loadDashboardData();
        }
      } else {
        this.isLoading = false;
        this.clearDashboardData();
      }
    }
  }

  /**
   * Check if all filters are loaded (used for UI state management)
   */
  get areAllFiltersLoaded(): boolean {
    return this.isConfigLoaded && this.isBranchesLoaded && this.isCategoriesLoaded && this.isSubcategoriesLoaded && this.isWorkAreasLoaded;
  }

  /**
   * Check if mapping-related filters are loaded (used for mapping button state)
   * Mapping buttons don't need work areas to be loaded, only basic config and categories
   */
  get areMappingFiltersLoaded(): boolean {
    const hasBasicRequirements = this.isConfigLoaded && this.isBranchesLoaded;
    const hasCategoriesOrData = this.isCategoriesLoaded || (this.categories && this.categories.length > 0);
    return hasBasicRequirements && hasCategoriesOrData;
  }

  /**
   * Reset filter loading states (used when dashboard type changes)
   */
  private resetFilterLoadingStates(): void {
    this.isCategoriesLoaded = false;
    this.isSubcategoriesLoaded = false;
    this.isWorkAreasLoaded = false;
  }

  /**
   * Schedule dashboard loading with debouncing to prevent multiple simultaneous calls
   */
  private scheduleDashboardLoad(): void {
    if (this.isDashboardApiCallInProgress || this.isLoading) {
      return;
    }

    if (this.dashboardLoadTimeout) {
      clearTimeout(this.dashboardLoadTimeout);
    }

    this.isDashboardLoadPending = true;

    this.dashboardLoadTimeout = setTimeout(() => {
      this.isDashboardLoadPending = false;
      this.checkAndLoadDashboard();
    }, 100);
  }

  private filterBranches(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredBranches = [...this.branches];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredBranches = this.branches.filter(branch =>
        branch.branchName.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm)
      );
    }
  }

  private loadCategoriesAndSubcategories(scheduleDashboard: boolean = true): void {
    if (!this.user?.tenantId) {
      return;
    }

    this.isInitialCategorySetup = true;

    this.smartDashboardService.getCategories(this.user.tenantId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (categoriesResponse) => {
          if (categoriesResponse && (categoriesResponse.success || categoriesResponse.result === 'success') && categoriesResponse.categories) {
            let categoryKeys = [];

            if (Array.isArray(categoriesResponse.categories)) {
              categoryKeys = categoriesResponse.categories;
            } else if (typeof categoriesResponse.categories === 'object') {
              categoryKeys = Object.keys(categoriesResponse.categories);
            }

            this.categories = [...categoryKeys];
            this.filteredCategories = [...categoryKeys];

            const categoriesToSend = this.categories.length > 0 ? this.categories : [];
            this.smartDashboardService.getSubCategories(this.user.tenantId, categoriesToSend)
              .pipe(takeUntil(this.destroy$))
              .subscribe({
                next: (subcategoriesResponse) => {
                  // Process subcategories
                  if (subcategoriesResponse && (subcategoriesResponse.success || subcategoriesResponse.result === 'success') && subcategoriesResponse.subcategories) {
                    let allSubcategories = [];

                    // Check if subcategories is an array or object
                    if (Array.isArray(subcategoriesResponse.subcategories)) {
                      allSubcategories = subcategoriesResponse.subcategories;
                    } else if (typeof subcategoriesResponse.subcategories === 'object') {
                      Object.values(subcategoriesResponse.subcategories).forEach((categorySubcats: any) => {
                        if (Array.isArray(categorySubcats)) {
                          allSubcategories.push(...categorySubcats);
                        }
                      });
                    }

                    // Force new array references to trigger change detection
                    const uniqueSubcategories = [...new Set(allSubcategories)];
                    this.subcategories = [...uniqueSubcategories];
                    this.filteredSubcategories = [...uniqueSubcategories];
                    this.setDefaultSubcategorySelection();

                    this.ngZone.run(() => {
                      this.cdr.detectChanges();
                    });
                  } else {
                    this.subcategories = [];
                    this.filteredSubcategories = [];
                  }

                  // Set default category selection AFTER subcategories are loaded to avoid duplicate API calls
                  this.setDefaultCategorySelection();

                  // Clear the initial setup flag after setting defaults
                  this.isInitialCategorySetup = false;

                  this.isCategoriesLoaded = true;
                  this.isSubcategoriesLoaded = true;
                  this.cdr.detectChanges();
                  if (scheduleDashboard) {
                    this.scheduleDashboardLoad();
                  }
                },
                error: () => {
                  this.subcategories = [];
                  this.filteredSubcategories = [];

                  // Set default category selection even on subcategory error
                  this.setDefaultCategorySelection();

                  // Clear the initial setup flag after setting defaults
                  this.isInitialCategorySetup = false;

                  this.isCategoriesLoaded = true;
                  this.isSubcategoriesLoaded = true;
                  this.cdr.detectChanges();
                  // Auto-load dashboard on initial page load
                  if (scheduleDashboard) {
                    this.scheduleDashboardLoad();
                  }
                }
              });
          } else {
            this.categories = [];
            this.filteredCategories = [];

            this.smartDashboardService.getSubCategories(this.user.tenantId, [])
              .pipe(takeUntil(this.destroy$))
              .subscribe({
                next: (subcategoriesResponse) => {
                  if (subcategoriesResponse && (subcategoriesResponse.success || subcategoriesResponse.result === 'success') && subcategoriesResponse.subcategories) {
                    let allSubcategories = [];

                    if (Array.isArray(subcategoriesResponse.subcategories)) {
                      allSubcategories = subcategoriesResponse.subcategories;
                    } else if (typeof subcategoriesResponse.subcategories === 'object') {
                      Object.values(subcategoriesResponse.subcategories).forEach((categorySubcats: any) => {
                        if (Array.isArray(categorySubcats)) {
                          allSubcategories.push(...categorySubcats);
                        }
                      });
                    }

                    this.subcategories = [...new Set(allSubcategories)];
                    this.filteredSubcategories = [...this.subcategories];
                    this.setDefaultSubcategorySelection();
                  } else {
                    this.subcategories = [];
                    this.filteredSubcategories = [];
                  }

                  // Clear the initial setup flag after loading subcategories
                  this.isInitialCategorySetup = false;

                  this.isCategoriesLoaded = true;
                  this.isSubcategoriesLoaded = true;
                  this.cdr.detectChanges();
                  // Auto-load dashboard on initial page load
                  if (scheduleDashboard) {
                    this.scheduleDashboardLoad();
                  }
                },
                error: () => {
                  this.subcategories = [];
                  this.filteredSubcategories = [];

                  // Clear the initial setup flag even on error
                  this.isInitialCategorySetup = false;

                  this.isCategoriesLoaded = true;
                  this.isSubcategoriesLoaded = true;
                  // Auto-load dashboard on initial page load
                  if (scheduleDashboard) {
                    this.scheduleDashboardLoad();
                  }
                }
              });
          }
        },
        error: () => {
          this.categories = [];
          this.filteredCategories = [];
          this.subcategories = [];
          this.filteredSubcategories = [];

          // Clear the initial setup flag even on error
          this.isInitialCategorySetup = false;

          // Mark as loaded even on error to prevent infinite waiting
          this.isCategoriesLoaded = true;
          this.isSubcategoriesLoaded = true;

          // Auto-load dashboard on initial page load
          if (scheduleDashboard) {
            this.scheduleDashboardLoad();
          }
        }
      });
  }

  private loadSubcategoriesForSelectedCategories(scheduleDashboard: boolean = true): void {
    if (!this.user?.tenantId) {
      return;
    }

    // Reset subcategories loading state when categories change
    this.isSubcategoriesLoaded = false;

    // Get selected categories
    const selectedCategories = this.selectedCategoriesCtrl.value || [];

    this.smartDashboardService.getSubCategories(this.user.tenantId, selectedCategories)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response['result'] == 'success') {
            const allSubcategories = response.subCategories;
            this.subcategories = [...new Set(allSubcategories)];
            this.filteredSubcategories = [...this.subcategories];

            // Set default subcategory selection
            this.setDefaultSubcategorySelection();

            // Mark subcategories as loaded
            this.isSubcategoriesLoaded = true;

            // Trigger change detection
            this.cdr.detectChanges();

            // Schedule dashboard loading with debouncing only if requested
            if (scheduleDashboard) {
              this.scheduleDashboardLoad();
            }
          } else {
            this.subcategories = [];
            this.filteredSubcategories = [];
            // Mark as loaded even on error to prevent infinite waiting
            this.isSubcategoriesLoaded = true;

            // Schedule dashboard loading with debouncing only if requested
            if (scheduleDashboard) {
              this.scheduleDashboardLoad();
            }
          }
        },
        error: () => {
          this.subcategories = [];
          this.filteredSubcategories = [];
          // Mark as loaded even on error to prevent infinite waiting
          this.isSubcategoriesLoaded = true;

          // Schedule dashboard loading with debouncing only if requested
          if (scheduleDashboard) {
            this.scheduleDashboardLoad();
          }
        }
      });
  }

  private filterCategories(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredCategories = [...this.categories];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredCategories = this.categories.filter(category =>
        category.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm)
      );
    }
  }

  private filterSubcategories(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredSubcategories = [...this.subcategories];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredSubcategories = this.subcategories.filter(subcategory =>
        subcategory.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm)
      );
    }
  }

  private filterWorkAreas(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredWorkAreas = [...this.workAreas];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredWorkAreas = this.workAreas.map(branch => ({
        ...branch,
        workAreas: branch.workAreas.filter((workArea: string) =>
          workArea.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm)
        )
      })).filter(branch => branch.workAreas.length > 0);
    }
  }

  private filterDepartments(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredDepartments = [...this.departments];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredDepartments = this.departments.filter(department =>
        department.name.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm) ||
        (department.code && department.code.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm))
      );
    }
  }

  // Track by function for ngFor performance
  trackByIndex(index: number): number {
    return index;
  }

  // Helper method for work areas count
  getTotalWorkAreasCount(): number {
    if (!this.filteredWorkAreas || this.filteredWorkAreas.length === 0) {
      return 0;
    }
    return this.filteredWorkAreas.flatMap(branch => branch.workAreas || []).length;
  }

  private setReconciliationDefaults(): void {
    setTimeout(() => {
      this.setDefaultLocationSelection();
      this.setDefaultWorkAreaSelection();
    }, 50);
  }

  private validateLocationSelection(selectedLocations: string[] | string | null): void {
    const isLimitedSelection = this.selectedDashboard === 'inventory' || this.selectedDashboard === 'purchase';

    if (isLimitedSelection && Array.isArray(selectedLocations) && selectedLocations.length > 3) {
      // Limit to first 3 selections for inventory and purchase dashboards
      const limitedSelection = selectedLocations.slice(0, 3);
      this.selectedLocationsCtrl.setValue(limitedSelection, { emitEvent: false });
    }
  }

  private setDefaultLocationSelection(): void {
    const isMultiSelect = this.selectedDashboard !== 'reconciliation';
    const isLimitedSelection = this.selectedDashboard === 'inventory' || this.selectedDashboard === 'purchase';

    // Always reset the form control to ensure proper type for the current dashboard
    if (isMultiSelect) {
      // For multi-select dashboards, ensure we have an array
      this.selectedLocationsCtrl.setValue([]);
    } else {
      // For reconciliation (single-select), ensure we start with null
      this.selectedLocationsCtrl.setValue(null);
    }

    if (this.branches.length > 0) {
      if (isMultiSelect) {
        if (isLimitedSelection) {
          // For inventory and purchase dashboards: select top 3 locations
          const top3Values = this.branches.slice(0, 3).map(branch => branch.restaurantIdOld);
          this.selectedLocationsCtrl.setValue(top3Values);
        } else {
          // For other multi-select dashboards: select all locations
          const allValues = this.branches.map(branch => branch.restaurantIdOld);
          this.selectedLocationsCtrl.setValue(allValues);
        }
      } else {
        // For reconciliation dashboard: select first branch
        const firstBranchId = this.branches[0].restaurantIdOld;
        this.selectedLocationsCtrl.setValue(firstBranchId);
      }
    } else {
      // No branches available
      const emptyValue = isMultiSelect ? [] : null;
      this.selectedLocationsCtrl.setValue(emptyValue);
    }

    // Force change detection to ensure UI updates
    this.cdr.detectChanges();
  }

  private setDefaultCategorySelection(): void {
    this.dropdownDefaults.setDefaultCategorySelection(
      this.selectedCategoriesCtrl,
      this.categories
    );
  }

  private setDefaultSubcategorySelection(): void {
    this.dropdownDefaults.setDefaultCategorySelection(
      this.selectedSubcategoriesCtrl,
      this.subcategories
    );
    this.cdr.detectChanges();
  }

  private setDefaultWorkAreaSelection(): void {
    if (this.selectedDashboard === 'reconciliation') {
      this.selectedWorkAreasCtrl.setValue([]);
    } else {
      this.dropdownDefaults.setDefaultWorkAreaSelection(
        this.selectedWorkAreasCtrl,
        this.workAreas,
        true,
        true
      );
    }
  }

  private setDefaultDepartmentSelection(): void {
    this.dropdownDefaults.setDefaultDepartmentSelection(
      this.selectedDepartmentsCtrl,
      this.departments
    );
  }

  // Select All / Deselect All methods for Restaurants
  areAllRestaurantsSelected(): boolean {
    const selectedLocations = this.getLocationsAsArray();
    const selectedCount = selectedLocations.length;
    const totalCount = this.filteredBranches.length;
    const isLimitedSelection = this.selectedDashboard === 'inventory' || this.selectedDashboard === 'purchase';

    if (isLimitedSelection) {
      // For inventory and purchase dashboards: consider "all" as top 3
      const maxSelectable = Math.min(3, totalCount);
      return selectedCount === maxSelectable && totalCount > 0;
    } else {
      // For other dashboards: all means all
      return selectedCount === totalCount && totalCount > 0;
    }
  }

  toggleAllRestaurants(event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    const isLimitedSelection = this.selectedDashboard === 'inventory' || this.selectedDashboard === 'purchase';

    if (this.areAllRestaurantsSelected()) {
      this.selectedLocationsCtrl.setValue([]);
    } else {
      if (isLimitedSelection) {
        // For inventory and purchase dashboards: select only top 3
        const top3RestaurantIds = this.filteredBranches.slice(0, 3).map(branch => branch.restaurantIdOld);
        this.selectedLocationsCtrl.setValue(top3RestaurantIds);
      } else {
        // For other dashboards: select all
        const allRestaurantIds = this.filteredBranches.map(branch => branch.restaurantIdOld);
        this.selectedLocationsCtrl.setValue(allRestaurantIds);
      }
    }
  }

  // Select All / Deselect All methods for Categories
  areAllCategoriesSelected(): boolean {
    const selectedCount = this.selectedCategoriesCtrl.value?.length || 0;
    const totalCount = this.filteredCategories.length;
    return selectedCount === totalCount && totalCount > 0;
  }

  toggleAllCategories(event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (this.areAllCategoriesSelected()) {
      this.selectedCategoriesCtrl.setValue([]);
    } else {
      this.selectedCategoriesCtrl.setValue([...this.filteredCategories]);
    }
  }

  // Select All / Deselect All methods for Subcategories
  areAllSubcategoriesSelected(): boolean {
    const selectedCount = this.selectedSubcategoriesCtrl.value?.length || 0;
    const totalCount = this.filteredSubcategories.length;
    return selectedCount === totalCount && totalCount > 0;
  }

  toggleAllSubcategories(event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (this.areAllSubcategoriesSelected()) {
      this.selectedSubcategoriesCtrl.setValue([]);
    } else {
      this.selectedSubcategoriesCtrl.setValue([...this.filteredSubcategories]);
    }
  }

  // Select All / Deselect All methods for Work Areas
  areAllWorkAreasSelected(): boolean {
    const selectedCount = this.selectedWorkAreasCtrl.value?.length || 0;
    const totalCount = this.getTotalWorkAreasCount();
    return selectedCount === totalCount && totalCount > 0;
  }

  toggleAllWorkAreas(event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (this.areAllWorkAreasSelected()) {
      this.selectedWorkAreasCtrl.setValue([]);
    } else {
      const allWorkAreas = this.filteredWorkAreas.flatMap(branch => branch.workAreas || []);
      this.selectedWorkAreasCtrl.setValue(allWorkAreas);
    }
  }

  // Select All / Deselect All methods for Departments
  areAllDepartmentsSelected(): boolean {
    const selectedCount = this.selectedDepartmentsCtrl.value?.length || 0;
    const totalCount = this.filteredDepartments.length;
    return selectedCount === totalCount && totalCount > 0;
  }

  toggleAllDepartments(event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (this.areAllDepartmentsSelected()) {
      this.selectedDepartmentsCtrl.setValue([]);
    } else {
      const allDepartmentIds = this.filteredDepartments.map(dept => dept.id);
      this.selectedDepartmentsCtrl.setValue(allDepartmentIds);
    }
  }

  // ===== MAPPING DIALOG METHODS =====

  openMappingDialog(): void {
    this.showMappingDialog = true;
  }

  closeMappingDialog(): void {
    this.showMappingDialog = false;
  }







  // ===== UNIFIED MAPPING HELPER METHODS =====

  /**
   * Get all categories that have been mapped to departments
   */
  getMappedCategories(): string[] {
    const mappedCategories = new Set<string>();

    if (this.departmentCategoryMappings && this.departmentCategoryMappings.length > 0) {
      this.departmentCategoryMappings.forEach(mapping => {
        if (mapping.categories && mapping.categories.length > 0) {
          mapping.categories.forEach(category => mappedCategories.add(category));
        }
      });
    }

    return Array.from(mappedCategories);
  }

  /**
   * Check if the unified mapping configuration is complete
   */
  isUnifiedMappingComplete(): boolean {
    // Check if departments are selected
    const hasDepartments = this.selectedDepartmentsCtrl.value && this.selectedDepartmentsCtrl.value.length > 0;

    // Check if department-category mappings are configured
    const hasDepartmentMappings = this.isDepartmentCategoryMappingConfigured();

    // Check if category-workarea mappings are configured
    const hasWorkareaMappings = this.isCategoryWorkareaMappingConfigured();

    return hasDepartments && hasDepartmentMappings && hasWorkareaMappings;
  }

  onDepartmentGroupMappingsChanged(mappings: DepartmentGroupCategoryMapping[]): void {
    // Store group mappings directly for API calls
    this.departmentGroupCategoryMappings = mappings;
    // Also convert to department mappings for backward compatibility with existing UI
    this.departmentCategoryMappings = this.convertGroupMappingsToDepartmentMappings(mappings);
  }

  private convertGroupMappingsToDepartmentMappings(groupMappings: DepartmentGroupCategoryMapping[]): DepartmentCategoryMapping[] {
    // For now, create placeholder department mappings from group mappings
    // In a full implementation, you would need to expand groups to their constituent departments
    return groupMappings.map(groupMapping => ({
      departmentId: groupMapping.groupId,
      departmentName: groupMapping.groupName,
      categories: groupMapping.categories
    }));
  }

  onCategoryWorkareaMappingsChanged(mappings: CategoryWorkareaMapping[]): void {
    this.categoryWorkareaMappings = mappings;
    this.updateWorkAreasFromMappings(mappings);
  }

  isMappingConfigurationAvailable(): boolean {
    return this.areMappingFiltersLoaded;
  }



  // Category selection methods for category-workarea mapping dialog
  areAllCategoriesSelectedForMapping(): boolean {
    const selectedCount = this.selectedCategoriesForMappingCtrl.value?.length || 0;
    const totalCount = this.filteredCategories.length;
    return selectedCount === totalCount && totalCount > 0;
  }

  toggleAllCategoriesForMapping(event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (this.areAllCategoriesSelectedForMapping()) {
      this.selectedCategoriesForMappingCtrl.setValue([]);
    } else {
      this.selectedCategoriesForMappingCtrl.setValue([...this.filteredCategories]);
    }
  }

  trackByCategory(_index: number, category: string): string {
    return category;
  }



  private updateWorkAreasFromMappings(mappings: CategoryWorkareaMapping[]): void {
    // Get all work areas from the mappings
    const mappedWorkAreas = this.getWorkAreasFromMappings(mappings);

    // Update the selected work areas control
    this.selectedWorkAreasCtrl.setValue(mappedWorkAreas);
  }

  /**
   * Extract work areas from category-workarea mappings
   * Returns physical workarea names only (not virtual ones)
   */
  private getWorkAreasFromMappings(mappings: CategoryWorkareaMapping[]): string[] {
    const workAreas = new Set<string>();

    mappings.forEach(mapping => {
      mapping.workAreas.forEach(workArea => {
        // With simplified mapping, workAreas contains the actual workareas to use
        workAreas.add(workArea);
      });
    });

    return Array.from(workAreas);
  }

  /**
   * Check if category-workarea mapping is configured
   * Always returns true since mapping is now optional
   */
  isCategoryWorkareaMappingConfigured(): boolean {
    return true; // Category-workarea mapping is now optional
  }

  /**
   * Check if department-category mapping is configured
   */
  isDepartmentCategoryMappingConfigured(): boolean {
    if (!this.departmentCategoryMappings || this.departmentCategoryMappings.length === 0) {
      return false;
    }

    // Check if at least one mapping has categories assigned
    const hasValidMappings = this.departmentCategoryMappings.some(mapping =>
      mapping.categories && mapping.categories.length > 0
    );

    return hasValidMappings;
  }







  /**
   * Get work areas data formatted for the category-workarea mapping component
   */
  getWorkAreasForMapping(): WorkAreaData[] {
    return this.workAreas.map(workArea => ({
      restaurantIdOld: workArea.restaurantIdOld,
      branchName: workArea.branchName,
      workAreas: workArea.workAreas,
      disabled: workArea.disabled || false
    }));
  }

  /**
   * Check if reconciliation dashboard has required department-category mappings configured
   */
  isReconciliationMappingConfigured(): boolean {
    if (this.selectedDashboard !== 'reconciliation') {
      return true; // Not applicable for other dashboards
    }

    // Check if we have any mappings configured
    if (!this.departmentCategoryMappings || this.departmentCategoryMappings.length === 0) {
      return false;
    }

    if (!this.categoryWorkareaMappings || this.categoryWorkareaMappings.length === 0) {
      return false;
    }

    return true;
  }


  /**
   * Show message to user that configuration is required
   */
  private showMappingRequiredMessage(): void {
    // Force the warning message to be visible by triggering change detection
    this.cdr.detectChanges();

    // Optional: Auto-scroll to the warning message
    setTimeout(() => {
      const warningElement = document.querySelector('.mapping-warning-container');
      if (warningElement) {
        warningElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }, 100);
  }

  loadDashboardData(): void {
    this.loadDashboardDataInternal();
  }

  private clearDashboardData(): void {
    this.summaryCards = [];
    this.charts = [];
  }

  private processDashboardData(data: any): void {
    // Process summary cards using smart dashboard service with Indian formatting
    this.summaryCards = data.summary_items?.map((item: any, index: number) => ({
      icon: item.icon || this.smartDashboardService.getSummaryCardIcon(item.data_type, item.label),
      value: this.formatCardValue(item.value, item.data_type),
      label: item.label,
      color: this.configService.getContextualColor(index, item.data_type),
      data_type: item.data_type,
      secondary_info: item.secondary_info,
      formula: item.formula
    })) || [];

    // Process charts using chart renderer service
    this.charts = data.charts?.map((chart: any) => {
      const processedChart = this.chartRenderer.processChart(chart);
      return processedChart;
    }) || [];
  }


  /**
   * Format card value based on data type with Indian numbering
   */
  private formatCardValue(value: any, dataType?: string): string {
    // Handle non-numeric values
    if (value === null || value === undefined || value === '') {
      return '0';
    }

    // If it's already a formatted string (contains letters), return as is
    if (typeof value === 'string' && /[a-zA-Z]/.test(value)) {
      return value;
    }

    // Convert to number for formatting
    const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^\d.-]/g, '')) : value;

    if (isNaN(numValue)) {
      return value.toString();
    }

    // Format based on data type
    if (dataType && (dataType.toLowerCase().includes('currency') || dataType.toLowerCase().includes('amount') || dataType.toLowerCase().includes('cost') || dataType.toLowerCase().includes('price'))) {
      return this.smartDashboardService.formatCurrency(numValue);
    } else {
      return this.smartDashboardService.formatExactNumber(numValue);
    }
  }

  private getLocationsAsArray(): string[] {
    const value = this.selectedLocationsCtrl.value;
    if (!value) return [];
    if (Array.isArray(value)) return value;
    return [value]; // Convert single string to array
  }

  // Helper method for template to get selected location count
  getSelectedLocationCount(): number {
    const value = this.selectedLocationsCtrl.value;
    if (!value) return 0;
    if (Array.isArray(value)) return value.length;
    return 1; // Single selection
  }

  getLocationPlaceholder(): string {
    const selectedCount = this.getSelectedLocationCount() || 0;
    const totalCount = this.filteredBranches?.length || 0;
    const isLimitedSelection = this.selectedDashboard === 'inventory' || this.selectedDashboard === 'purchase';

    if (isLimitedSelection) {
      const maxSelectable = Math.min(3, totalCount);
      return `Select restaurants (${selectedCount}/${maxSelectable}) - Max 3`;
    } else {
      return `Select restaurants (${selectedCount}/${totalCount})`;
    }
  }

  getSelectAllButtonText(): string {
    const isLimitedSelection = this.selectedDashboard === 'inventory' || this.selectedDashboard === 'purchase';

    if (this.areAllRestaurantsSelected()) {
      return 'Deselect All';
    } else {
      if (isLimitedSelection) {
        return 'Select Top 3';
      } else {
        return 'Select All';
      }
    }
  }

  isLocationOptionDisabled(locationId: string): boolean {
    const isLimitedSelection = this.selectedDashboard === 'inventory' || this.selectedDashboard === 'purchase';

    if (!isLimitedSelection) {
      return false; // No restrictions for other dashboards
    }

    const selectedLocations = this.getLocationsAsArray();
    const isCurrentlySelected = selectedLocations.includes(locationId);

    // If this location is already selected, it should not be disabled
    if (isCurrentlySelected) {
      return false;
    }

    // If we haven't reached the limit, don't disable
    if (selectedLocations.length < 3) {
      return false;
    }

    // We've reached the limit and this location is not selected, so disable it
    return true;
  }

  isSelectAllButtonDisabled(): boolean {
    const isLimitedSelection = this.selectedDashboard === 'inventory' || this.selectedDashboard === 'purchase';

    if (!isLimitedSelection) {
      return false; // No restrictions for other dashboards
    }

    const selectedLocations = this.getLocationsAsArray();

    // If we already have the maximum selected and it's not "all selected", disable the button
    // This prevents clicking "Select Top 3" when we already have 3 selected but they're not the top 3
    if (selectedLocations.length >= 3 && !this.areAllRestaurantsSelected()) {
      return true;
    }

    return false;
  }

  private formatDate(date: Date): string {
    // Fix the date offset issue by using local date components
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  private setDefaultDateRange(): void {
    // Only set date range if we have valid configuration and dashboard selection
    if (!this.selectedDashboard || this.dashboardTypes.length === 0) {
      return;
    }

    const today = new Date();
    // For both dashboards: current month start to current date
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    this.startDate.setValue(startOfMonth);
    this.endDate.setValue(today);
  }

  onLocationChange(): void {
    // No automatic API call - user must click Search button
  }

  onDateChange(): void {
    // No automatic API call - user must click Search button
  }

  onDashboardChange(): void {
    this.clearDashboardData();
    this.isLoading = false;

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { dashboard: this.selectedDashboard },
      queryParamsHandling: 'merge'
    });

    this.setDefaultDateRange();
    this.resetFilterLoadingStates();
    this.cdr.detectChanges();

    this.setDefaultLocationSelection();
    this.loadWorkAreasFromBranches(false);

    // For reconciliation dashboard, don't auto-load data - user must click Search button
    const shouldAutoLoad = this.selectedDashboard !== 'reconciliation';
    this.loadCategoriesAndSubcategories(shouldAutoLoad);

    if (this.selectedDashboard === 'reconciliation') {
      this.loadDepartments();
      this.loadCategoryWorkareaMappings();
    } else {
      this.selectedDepartmentsCtrl.setValue([]);
    }

    this.cdr.detectChanges();
  }

  searchDashboard(): void {
    this.loadDashboardData();
  }

  resetFilters(): void {
    // Reset filters using default selection methods
    this.setDefaultLocationSelection();
    this.setDefaultCategorySelection();
    this.setDefaultSubcategorySelection();
    this.setDefaultWorkAreaSelection();
    this.setDefaultDepartmentSelection();

    // Only set defaults if we have valid configuration
    if (this.selectedDashboard) {
      this.setDefaultDateRange();
    }

    // Only set base date default if we have options available
    if (this.baseDateOptions.length > 0) {
      const defaultBaseDate = this.baseDateOptions[0]?.value;
      if (defaultBaseDate) {
        this.baseDateCtrl.setValue(defaultBaseDate);
      }
    }

    this.searchQuery.setValue('');
    this.locationFilterCtrl.setValue('');
    this.categoryFilterCtrl.setValue('');
    this.subcategoryFilterCtrl.setValue('');
    this.workAreaFilterCtrl.setValue('');
    this.filteredBranches = [...this.branches];
    this.filteredCategories = [...this.categories];
    this.filteredSubcategories = [...this.subcategories];
    this.filteredWorkAreas = [...this.workAreas];

    // Load dashboard data with reset filters
    this.loadDashboardData();
  }

  onSearchQuery(): void {
    const query = this.searchQuery.value?.trim();
    if (query) {
      this.loadDashboardDataWithQuery(query);
    } else {
      this.loadDashboardData();
    }
  }

  private loadDashboardDataWithQuery(query: string): void {
    this.loadDashboardDataInternal(query, false);
  }

  private loadDashboardDataInternal(userQuery: string = '', useDefaultCharts: boolean = true): void {
    if (this.isLoading || this.isDashboardApiCallInProgress) {
      return;
    }

    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {
      this.isLoading = false;
      this.clearDashboardData();
      this.cdr.detectChanges();
      return;
    }



    this.isLoading = true;
    this.isDashboardApiCallInProgress = true;

    const selectedCategories = this.selectedCategoriesCtrl.value || [];
    const selectedSubcategories = this.selectedSubcategoriesCtrl.value || [];
    const selectedWorkAreas = this.selectedWorkAreasCtrl.value || [];
    const selectedDepartments = this.selectedDepartmentsCtrl.value || [];

    // For reconciliation dashboard, gather workAreas from category_workarea_mappings
    let workAreasToSend: string[] = [];
    if (this.selectedDashboard === 'reconciliation' && this.categoryWorkareaMappings && this.categoryWorkareaMappings.length > 0) {
      workAreasToSend = this.getWorkAreasFromMappings(this.categoryWorkareaMappings);
    } else {
      workAreasToSend = selectedWorkAreas;
    }

    const filters = {
      locations: this.getLocationsAsArray(),
      startDate: this.formatDate(this.startDate.value),
      endDate: this.formatDate(this.endDate.value),
      baseDate: this.baseDateCtrl.value || 'deliveryDate',
      categories: selectedCategories.length > 0 ? selectedCategories : undefined,
      subcategories: selectedSubcategories.length > 0 ? selectedSubcategories : undefined,
      workAreas: workAreasToSend.length > 0 ? workAreasToSend : undefined,
      departments: selectedDepartments.length > 0 ? selectedDepartments : undefined
    };

    const request = {
      tenant_id: this.user.tenantId,
      filters: filters,
      user_query: userQuery,
      use_default_charts: useDefaultCharts,
      dashboard_type: this.selectedDashboard
      // Note: Mappings are now fetched directly from database by backend
    };

    // Note: Mappings are now fetched directly from database by backend

    this.smartDashboardService.getSmartDashboardData(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.processDashboardData(response.data);
          } else {
            this.clearDashboardData();
            if (response.error_code === 'MAPPING_REQUIRED' || response.error_code === 'INVALID_MAPPING') {
              this.showMappingRequiredMessage();
            }
          }
          this.isLoading = false;
          this.isDashboardApiCallInProgress = false;

          if (this.selectedDashboard === 'reconciliation') {
            this.setReconciliationDefaults();
          }

          this.cdr.detectChanges();
        },
        error: (error) => {
          this.clearDashboardData();
          if (error.error && (error.error.error_code === 'MAPPING_REQUIRED' || error.error.error_code === 'INVALID_MAPPING')) {
            this.showMappingRequiredMessage();
          }
          this.isLoading = false;
          this.isDashboardApiCallInProgress = false;

          if (this.selectedDashboard === 'reconciliation') {
            this.setReconciliationDefaults();
          }

          this.cdr.detectChanges();
        }
      });
  }

  // Dynamic chart methods using services
  getChartData(chart: ChartModel): ChartData {
    return chart.data;
  }

  getChartType(chart: ChartModel): ChartType {
    return this.chartRenderer.getChartType(chart.type);
  }

  getChartOptions(chart: ChartModel): ChartConfiguration['options'] {
    return chart.options || this.configService.getDefaultChartOptions();
  }

  getChartCssClass(chart: ChartModel): string {
    return this.chartRenderer.getChartCssClass(chart);
  }

  // Floating navigation methods for reconciliation dashboard
  getReconciliationTables(): ChartModel[] {
    if (this.selectedDashboard !== 'reconciliation') {
      return [];
    }

    // Filter only table charts for navigation
    return this.charts.filter(chart => chart.type === 'table');
  }

  getShortTableName(title: string): string {
    // Convert long table titles to short navigation labels
    const titleMap: { [key: string]: string } = {
      'Reconciliation: Opening + Store Transfer + Workareas Transfer - Closing = Consumption': 'Main',
      'Transfer In/Out (Workareas): Transfer In - Transfer Out - Return To Store + Spoilage + Cross-Category Indents': 'Workarea',
      'Transfer In/Out (Store): Purchase + IBT In - IBT Out - Return Qty + Spoilage': 'Store',
      'Cross-Category Transfer Details: Source → Destination Visibility': 'Transfer Details',
      'Food Cost Analysis: Department-wise Cost vs Sales Analysis': 'Cost Analysis'
    };

    // Check for exact matches first
    for (const [fullTitle, shortName] of Object.entries(titleMap)) {
      if (title.includes(fullTitle.split(':')[0])) {
        return shortName;
      }
    }

    // Fallback: extract key words
    if (title.includes('Reconciliation')) return 'Main';
    if (title.includes('Workareas') || title.includes('Workarea')) return 'Workarea';
    if (title.includes('Store')) return 'Store';
    if (title.includes('Food Cost') || title.includes('Cost Analysis')) return 'Cost Analysis';

    // Default: use first 2 words
    return title.split(' ').slice(0, 2).join(' ');
  }

  scrollToChart(chartId: string): void {
    const element = document.getElementById('chart-' + chartId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });

      // Add a brief highlight effect
      element.classList.add('chart-highlight');
      setTimeout(() => {
        element.classList.remove('chart-highlight');
      }, 2000);
    }
  }

  toggleNavigation(): void {
    this.isNavMinimized = !this.isNavMinimized;
  }

  // Enhanced helper methods for table charts
  getTableHeaders(chart: ChartModel): string[] {
    return (chart.data as any)?.headers || [];
  }

  getTableRows(chart: ChartModel): any[] {
    return (chart.data as any)?.rows || [];
  }

  getTableOptions(chart: ChartModel): any {
    return (chart as any)?.options || {};
  }

  // Material table support methods
  getTableDataSource(chart: ChartModel): any[] {
    return this.getFilteredTableRows(chart);
  }

  getDisplayedColumns(chart: ChartModel): string[] {
    const headers = this.getTableHeaders(chart);
    return headers.map((_, index) => 'col' + index);
  }

  // Table filtering and search
  private tableFilters: { [chartId: string]: string } = {};

  getFilteredTableRows(chart: ChartModel): any[] {
    const rows = this.getTableRows(chart);
    const filter = this.tableFilters[chart.id];

    if (!filter || filter.trim() === '') {
      return rows;
    }

    const filterLower = filter.toLowerCase();
    return rows.filter(row => {
      return Object.values(row).some(value =>
        value && value.toString().toLowerCase().includes(filterLower)
      );
    });
  }

  applyTableFilter(event: Event, chart: ChartModel): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.tableFilters[chart.id] = filterValue;
  }

  // Table styling methods - REMOVED INLINE STYLING
  // All styling is now handled by CSS classes for better performance and consistency

  formatTableCell(value: any, header: string, row: any): string {
    if (value === null || value === undefined || value === '' || value === '-') {
      return '<span class="empty-cell">-</span>';
    }

    if (header === 'Expense Head/Item' && typeof value === 'string' && value.startsWith('  ')) {
      return `<span class="subcategory-item">${value}</span>`;
    }

    // Handle special row types with bold formatting
    if (row && (row._isDepartmentSales || row._isDepartmentTotal || row._isOverallSales ||
      row._isOverallTotal || row._isGrandTotal || row._isCategoryRow || row._isDepartment || row._isCategory)) {
      return `<strong>${value}</strong>`;
    }

    // Handle Cross-Category Indents column with hover details
    if (header === 'Cross-Category Indents (₹)' && row && row._crossCategoryHover) {
      const cleanValue = value.replace(/[✅⚠️]/g, '').trim();
      const hasValidationIcon = /[✅⚠️]/.test(value);
      const validationIcon = hasValidationIcon ? value.match(/[✅⚠️]/)[0] : '';
      const hoverDetails = row._crossCategoryHover.replace(/"/g, '&quot;').replace(/\n/g, '&#10;');

      return `<span class="cross-category-cell" title="${hoverDetails}">${validationIcon} ${cleanValue}</span>`;
    }

    // Handle currency columns (headers contain ₹)
    if (typeof header === 'string' && header.includes('(₹)') && typeof value === 'string') {
      // Check if value is numeric (with commas) or has validation icons
      const cleanValue = value.replace(/[✅⚠️]/g, '').trim();
      if (/^[\d,]+(\.\d+)?$/.test(cleanValue.replace(/,/g, ''))) {
        return `<span class="currency-cell">${value}</span>`;
      }
    }

    // Handle percentage columns (headers contain %)
    if (typeof header === 'string' && header.includes('%') && typeof value === 'string') {
      // Check if value is numeric
      if (/^\d+(\.\d+)?$/.test(value)) {
        return `<span class="percentage-cell">${value}%</span>`;
      }
    }

    return value;
  }

  // Get plain text content for tooltip (without HTML tags)
  getPlainTextContent(value: any, header: string): string {
    if (value === null || value === undefined || value === '' || value === '-') {
      return '';
    }

    // Handle subcategory items
    if (header === 'Expense Head/Item' && typeof value === 'string' && value.startsWith('  ')) {
      return value.trim();
    }

    // Handle percentage columns
    if (typeof header === 'string' && header.includes('%') && typeof value === 'string') {
      if (/^\d+(\.\d+)?$/.test(value)) {
        return `${value}%`;
      }
    }

    // Return clean text without HTML tags
    return value.toString().replace(/<[^>]*>/g, '').trim();
  }

  // Export functionality
  exportTable(chart: ChartModel): void {
    const headers = this.getTableHeaders(chart);
    const rows = this.getFilteredTableRows(chart);

    // Create CSV content
    const csvContent = [
      headers.join(','),
      ...rows.map(row =>
        headers.map(header => {
          let value = row[header] || '';
          // Clean up formatting for CSV
          value = value.toString().replace(/\*\*/g, '').replace(/<[^>]*>/g, '');
          // Escape commas and quotes
          if (value.includes(',') || value.includes('"')) {
            value = `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',')
      )
    ].join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${chart.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  // Dashboard mode methods
  isSearchBarDisabled(): boolean {
    return this.dashboardModeCtrl.value === 'default' || this.isAiModeDisabled();
  }

  isAiModeDisabled(): boolean {
    return this.dashboardModes.find(mode => mode.value === 'ask_digi_ai')?.disabled || false;
  }

  setDashboardMode(mode: string): void {
    if (mode === 'ask_digi_ai' && this.isAiModeDisabled()) {
      // Don't allow switching to AI mode if it's disabled
      return;
    }
    this.dashboardModeCtrl.setValue(mode);
  }

  // Cost Analysis Toggle Methods
  toggleCostAnalysisMode(): void {
    if (this.isCostAnalysisToggleLoading) {
      return;
    }

    // Toggle between consumption and indent values
    this.costAnalysisMode = this.costAnalysisMode === 'consumption' ? 'indent' : 'consumption';
    this.switchCostAnalysisData();
  }

  private switchCostAnalysisData(): void {
    if (this.selectedDashboard !== 'reconciliation') {
      return;
    }

    this.isCostAnalysisToggleLoading = true;

    // Find the cost analysis table in the charts array
    const costAnalysisIndex = this.charts.findIndex(chart => chart.id === 'cost_analysis');

    if (costAnalysisIndex !== -1) {
      const costAnalysisChart = this.charts[costAnalysisIndex];

      // Check if toggle data is available
      if (costAnalysisChart.rawData?.toggle_data) {
        const toggleData = costAnalysisChart.rawData.toggle_data;
        const targetData = this.costAnalysisMode === 'consumption' ? toggleData.consumption : toggleData.indent;

        if (targetData) {
          // Update the chart data
          costAnalysisChart.data = targetData;

          // Update the title and subtitle
          const modeLabel = this.costAnalysisMode === 'consumption' ? 'Consumption Values' : 'Indent Values';
          costAnalysisChart.title = costAnalysisChart.title.replace(/(Consumption Values|Indent Values)/, modeLabel);
          costAnalysisChart.subtitle = costAnalysisChart.subtitle?.replace(/(Consumption Values|Indent Values)/, modeLabel);

          this.cdr.detectChanges();
        }
      }
    }

    // Clear loading state immediately since it's just a data switch
    this.isCostAnalysisToggleLoading = false;
  }

  getCostAnalysisModeLabel(): string {
    return this.costAnalysisMode === 'consumption' ? 'Consumption Values' : 'Indent Values';
  }

  getCostAnalysisToggleTooltip(): string {
    const currentMode = this.getCostAnalysisModeLabel();
    const nextMode = this.costAnalysisMode === 'consumption' ? 'Indent Values' : 'Consumption Values';
    return `Currently showing: ${currentMode}. Click to switch to: ${nextMode}`;
  }

  isCostAnalysisTable(chart: ChartModel): boolean {
    return chart.id === 'cost_analysis' && chart.type === 'table';
  }

  // Expose window object for template access
  window = window;
}
